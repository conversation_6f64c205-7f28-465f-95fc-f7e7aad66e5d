import React, { useState, useEffect } from "react";
import { List, Switch, Toast } from "antd-mobile";
import { useHistory } from "react-router-dom";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import NavigatorBar from "@/components/NavBar";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { setFacialRecognition, getFacialInfo, getFacialRecognition } from "@/api/ipc";


const FaceIdentifyPage = () => {
  const history = useHistory();
  const { isDarkMode } = useTheme();

  const [faceRecognitionEnabled, setFaceRecognitionEnabled] = useState(false);
  const [faceCount, setFaceCount] = useState(0);
  
  // 获取人脸识别设置
  const { run: fetchRecognitionStatus } = useRequest(
    getFacialRecognition,
    {
      onSuccess: (res) => {
        if (res && res.code === 0) {
          setFaceRecognitionEnabled(res.data.enabled);
        } else {
          Toast.show({
            content: res?.result,
          });
        }
      },
      onError: (error) => {
        console.error("获取人脸识别设置失败:", error);
      }
    }
  );
  
  // 获取人脸信息
   useRequest(
    getFacialInfo,
    {
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 计算有name值的人脸数量（已标记的）
          const markedFaces = res.data.info.filter(face => face.name && face.name.trim() !== "");
          setFaceCount(markedFaces.length);
        } else {
         Toast.show({
            content: res?.result,
          });
        }
      },
      onError: (error) => {
        console.error("获取人脸信息失败:", error);
        Toast.show({
          content: "获取人脸信息失败，使用默认数据",
        });
     
      }
    }
  );
  
  // 开启/关闭人脸识别
  const { run: toggleFaceRecognition } = useRequest(
    setFacialRecognition,
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 设置成功后更新状态
          fetchRecognitionStatus(); // 重新获取最新状态
          Toast.show({
            content: "设置成功",
            duration: 1000,
          });
        } else {
          Toast.show({
            content: res?.result,
          });
        }
      },
      onError: (error) => {
        console.error("设置人脸识别失败:", error);
        Toast.show({
          content: "设置失败，请重试",
        });
      }
    }
  );
  
  // 页面曝光埋点
  useEffect(() => {
    window.onetrack?.('track', 'ipc_faceRecognition_expose');
  }, []);

  // 处理开关变化
  const handleSwitchChange = (checked: boolean) => {
    // 添加人脸识别开关埋点
    if (checked) {
      window.onetrack?.('track', 'ipc_faceRecognition_enable');
    } else {
      window.onetrack?.('track', 'ipc_faceRecognition_disable');
    }

    // 调用接口更新设置，成功后会重新获取状态
    toggleFaceRecognition({ enabled: checked });
  };
  
  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>AI功能</div>

      <div className={styles.description}>
        搭载小米人工智能实验室DNN神经网络算法，快速识别图像，帮助您了解中人员情况。
      </div>

      <div className={styles.content}>
        <List className={styles.list}>
          <List.Item
            className={styles.switchItem}
            extra={
              <Switch
                checked={faceRecognitionEnabled}
                onChange={handleSwitchChange}
                style={{
                  "--checked-color": "#3482ff",
                }}
              />
            }
          >
            人脸识别
          </List.Item>

          <List.Item
            className={styles.item}
            arrow={true}
            extra={<span className={styles.extraText}>{`已录入${faceCount}个`}</span>}
            onClick={() => {
              history.push("/cameraManagement_app/faceRecognition/faceManagement");
            }}
          >
            人脸管理
          </List.Item>
        </List>
      </div>
    </div>
  );
};

export default FaceIdentifyPage;
