.synchronizationContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color, #ffffff);
}

.fixedHeader {
  flex-shrink: 0;
  background-color: var(--background-color, #ffffff);
}

.title {
  font-size: 24px;
  padding: 0 16px;
  color: var(--text-color, #000000);
  margin-bottom: 12px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--background-color, #ffffff);
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 8px;
  position: relative;
  justify-content: space-between;

  &::-webkit-scrollbar {
    display: none;
  }

  .breadcrumbPath {
    display: flex;
    align-items: center;
    flex: 1;
    overflow-x: auto;
    white-space: nowrap;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .breadcrumbContent {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .breadcrumbNextContainer {
    display: flex;
    align-items: center;
    margin: 0 3px;
  }

  .breadcrumbNextImg {
    height: 16px;
  }

  .breadcrumbItem {
    font-size: 14px;
    color: rgba(140, 147, 176, 1);
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 20px;
    background-color: rgba(140, 147, 176, 0.1);
    flex-shrink: 0;
    display: inline-block;

    &:hover {
      opacity: 0.8;
    }
  }

  .breadcrumbItemActive {
    color: rgba(255, 178, 29, 1) !important;
    font-weight: 500 !important;
    background-color: rgba(255, 178, 29, 0.15) !important;
  }

  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--secondary-text-color, #8c93b0);
    font-size: 12px;
    flex-shrink: 0;
  }

  .selectAllCheckbox {
    flex-shrink: 0;
    margin-left: auto;
  }
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; // 为底部按钮留出空间
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--secondary-text-color, #8c93b0);

  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  padding: 0 16px;

  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .fileContent {
      display: flex;
      align-items: center;
      flex: 1;
      cursor: pointer;
    }

    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .fileInfo {
      flex: 1;
      overflow: hidden;

      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color, #000000);
        margin-bottom: 4px;
        overflow: hidden;
        word-break: break-all; /* 强制在任意字符处换行 */
        word-wrap: break-word; /* 兼容性属性 */
        overflow-wrap: break-word; /* 现代标准属性 */

        .heartIcon {
          margin-left: 8px;
          color: #ff5c5c;
          font-size: 14px;
          flex-shrink: 0;
        }
      }

      .fileDetails {
        font-size: 12px;
        color: var(--secondary-text-color, #8c93b0);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .checkboxContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 12px;
    }
  }

  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8c93b0);
    font-size: 14px;
  }
}

.footerButtons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 16px;
  background-color: var(--background-color);
  .locationButton {
    border-radius: 30px;
    background-color: transparent;
    border-color: var(--thinLine-background-color);
    color: var(--primary-color);
    margin-right: 16px;
    font-size: 12px;
    height: 50px;
    padding: 5px 16px;
    text-align: left;
  }
  .downloadButton {
    background-color: var(--primary-color);
    border-radius: 30px;
    border: none;
    max-width: 168px;
  }
  :global {
    .adm-button:active::before {
      opacity: 0;
    }
    .adm-button-disabled {
      background-color: var(--primary-color);
      opacity: 0.3;
    }
  }

  .vipTip {
    position: absolute;
    top: 0;
    left: 45%;
    transform: translateX(-50%);
    background-color: #402c00;
    color: #e2ae1e;
    padding: 2px 5px;
    border-radius: 5px;
    font-size: 12px;
    border: 0.1px solid #e2ae1e;
    z-index: 10;
  }
}
.pathText {
  display: -webkit-box;
  display: -moz-box; /* 部分旧版 Firefox 需要 */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical; /* Firefox 兼容 */
  -webkit-line-clamp: 2; /* WebKit/Blink 核心 */
  line-clamp: 2; /* 标准属性 */
  box-orient: vertical; /* 备用标准属性 (部分场景) */
}
