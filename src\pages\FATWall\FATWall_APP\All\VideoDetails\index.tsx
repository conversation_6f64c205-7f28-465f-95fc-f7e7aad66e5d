import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import moreIcon from '@/Resources/filmWall/more.png';
import updon from '@/Resources/player/updon.png';
import no_detaile from '@/Resources/filmWall/no_detaile.png'
import no_avatar from '@/Resources/filmWall/no_avatar.png'
import { FilmCardList, IFilmCard } from "../../../../../components/FATWall_APP/FilmCard";
import { Button, Tag, Popover, Toast, Divider } from 'antd-mobile';
import { PlayOutline, HeartOutline, HeartFill, CheckOutline, DownlandOutline } from 'antd-mobile-icons';
import douBan_icon from '@/Resources/filmWall/douban_icon.png';
import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import NavigatorBar from '@/components/NavBar';
import { useHistory, useLocation } from 'react-router-dom';
import SearchSelector, { SearchStatus } from './SearchSelector';
import { modalShow } from '@/components/List';
import { px2rem } from '@/utils/setRootFontSize';
import { searchOnlineMedia, mediaProps, rematchMedia, mediaDelete, move2trashbin, getMediaDetails, MediaDetailsResponse, getMediaFiles, MediaFileInfo, collect, markWatched, getFilePath } from '@/api/fatWall';
import { useRequest } from 'ahooks';
import { playVideo, downloadFiles } from '@/api/fatWallJSBridge';
import request from '@/request';
import CommonUtils from '@/utils/CommonUtils';

// 类型定义
interface Actor {
    name?: string;
    role?: string;
    poster?: string;
}

interface FileInfo {
    path: string;
    size: string;
}

// 版本类型定义
interface Version {
    id: string;
    name: string;
    path?: string; // 添加可选的path字段用于播放
}

// 添加格式化文件大小的函数
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

interface VideoInfo {
    title: string;
    score: string | number;
    year: string;
    category: string;
    duration: string;
    source: string;
    posterUrl: string;
    actors: Actor[];
    fileInfo: FileInfo;
    synopsis: string;
    versions: Version[];
    video_length: string;
}

interface VideoDetailsProps {
    path: string;
}

const VideoDetails: React.FC<VideoDetailsProps> = (props) => {
    const history = useHistory();
    const location = useLocation();

    // 支持URL参数和state参数两种方式
    const urlParams = new URLSearchParams(location.search);
    const urlClasses = urlParams.get('classes');
    const urlMediaId = urlParams.get('media_id');
    const urlLibId = urlParams.get('lib_id');
    const urltitle = urlParams.get('title')
    // const urlIsDrama = urlParams.get('isDrama');
    // const urlIsDrama = urlParams.get('isDrama');

    // 优先使用URL参数，如果没有则使用state参数
    const stateParams = location.state as { classes: string, media_id: number, lib_id: number, isDrama?: boolean, fromLibrary?: boolean, libraryTitle?: string } || {};
    // console.log(stateParams);

    const classes = urlClasses || stateParams.classes || '';
    const media_id = urlMediaId ? parseInt(urlMediaId) : (stateParams.media_id || 0);
    const lib_id = urlLibId ? parseInt(urlLibId) : (stateParams.lib_id || 0);
    const lib_title = urltitle? urltitle : stateParams.libraryTitle || '媒体库';
    // const isDrama = urlIsDrama === 'true' || stateParams.isDrama || false;
    // const isDrama = urlIsDrama === 'true' || stateParams.isDrama || false;
    const [headerStyle, setHeaderStyle] = useState<React.CSSProperties>({});
    const [showFullSynopsis, setShowFullSynopsis] = useState<boolean>(false);
    const [isFavorite, setIsFavorite] = useState<boolean>(false);
    const [isCheck, setIsCheck] = useState<boolean>(false);
    const [selectedVersionId, setSelectedVersionId] = useState<string>('1');
    const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);
    const [searchKeyword, setSearchKeyword] = useState<string>('');
    const [searchStatus, setSearchStatus] = useState<SearchStatus>('idle');
    const [searchResults, setSearchResults] = useState<mediaProps[]>([]);
    const [selectedMatch, setSelectedMatch] = useState<mediaProps | null>(null);
    const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
    const [morePopoverVisible, setMorePopoverVisible] = useState<boolean>(false);
    const [mediaDetails, setMediaDetails] = useState<MediaDetailsResponse | null>(null);
    const [mediaFiles, setMediaFiles] = useState<MediaFileInfo[]>([]);
    const [episodeThumbnails, setEpisodeThumbnails] = useState<Map<number, string>>(new Map()); // 存储每集的缩略图
    const synopsisRef = useRef<HTMLDivElement>(null);
    const [showRefreshToast, setShowRefreshToast] = useState<boolean>(false);
    const [, setCurrentTaskId] = useState<string>(''); // 存储当前任务ID
    const [hasDownloaded, setHasDownloaded] = useState<boolean>(false); // 是否已经下载过

    // 获取媒体详情 - 使用仅图标loading模式
    const { run: runGetMediaDetails, } = useRequest(
        (params) => getMediaDetails(params, { loadingMode: 'icon' }),
        {

            manual: true,
            onSuccess: (res) => {
                if (res.code === 0 && res.data) {
                    setMediaDetails(res.data);
                    // 更新收藏和已看状态
                    setIsFavorite(res.data.favourite === 1);
                    setIsCheck(res.data.seen === 1);

                    // 数据刷新成功提示
                    if (showRefreshToast) {
                        Toast.show({
                            content: '信息已更新',
                            position: 'bottom',
                            duration: 1500,
                        });
                        setShowRefreshToast(false);
                    }
                }
            },
            onError: () => {
                Toast.show({
                    content: '获取影视详情失败',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );

    // 获取媒体文件列表 - 使用仅图标loading模式
    const { run: runGetMediaFiles, } = useRequest(
        (params) => getMediaFiles(params, { loadingMode: 'icon' }),
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0 && res.data) {
                    setMediaFiles(res.data.files || []);

                }
            },
            onError: () => {
                Toast.show({
                    content: '获取文件列表失败',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );

    // 收藏/取消收藏
    const { run: runCollect } = useRequest(
        collect,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0) {
                    Toast.show({
                        content: isFavorite ? '已添加到收藏' : '已取消收藏',
                        position: 'bottom',
                        duration: 1500,
                    });
                } else if (res.code === 2205) {
                    Toast.show({
                        content: '目标影视项不存在',
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 恢复状态
                    setIsFavorite(prev => !prev);
                } else {
                    Toast.show({
                        content: '操作失败，请重试',
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 恢复状态
                    setIsFavorite(prev => !prev);
                }
            },
            onError: () => {
                Toast.show({
                    content: '操作失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
                // 恢复状态
                setIsFavorite(prev => !prev);
            },
        }
    );

    // 标记已观看/未观看
    const { run: runMarkWatched } = useRequest(
        markWatched,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0) {
                    Toast.show({
                        content: isCheck ? '标记为已观看' : '标记为未观看',
                        position: 'bottom',
                        duration: 1500,
                    });
                } else if (res.code === 2205) {
                    Toast.show({
                        content: '目标影视项不存在',
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 恢复状态
                    setIsCheck(prev => !prev);
                } else {
                    Toast.show({
                        content: '操作失败，请重试',
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 恢复状态
                    setIsCheck(prev => !prev);
                }
            },
            onError: () => {
                Toast.show({
                    content: '操作失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
                // 恢复状态
                setIsCheck(prev => !prev);
            },
        }
    );

    // 获取缩略图 - 支持二进制响应，静默更新
    const getThumbnailWithBinary = (params: { path: string, size: string }) => {
        return request.post('/filemgr/get_thumbnail', params, {
            responseType: 'arraybuffer',
            showLoading: false // 静默更新，不显示loading
        });
    };

    // 获取所有集数的缩略图 - 串行版本，加载完一张设置一张
    const fetchAllThumbnails = useCallback(async () => {
        if (mediaFiles && mediaFiles.length > 0) {
            try {
                // 串行获取每个缩略图，避免并发请求导致进程卡死
                for (const file of mediaFiles) {
                    try {
                        console.log(`正在获取第${file.episode}集的缩略图...`);

                        const res = await getThumbnailWithBinary({
                            path: file.path,
                            size: "medium"
                        });

                        if (res instanceof ArrayBuffer) {
                            // 检查JPEG文件头
                            const uint8Array = new Uint8Array(res);
                            const isValidJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF;

                            let imageUrl: string;
                            if (isValidJPEG) {
                                const blob = new Blob([res], { type: 'image/jpeg' });
                                imageUrl = URL.createObjectURL(blob);
                            } else {
                                // 尝试作为其他格式
                                const blob = new Blob([res], { type: 'image/png' });
                                imageUrl = URL.createObjectURL(blob);
                            }

                            // 加载完一张立即设置一张，提供更好的用户体验
                            setEpisodeThumbnails(prev => {
                                const newMap = new Map(prev);
                                newMap.set(file.file_id, imageUrl);
                                return newMap;
                            });

                            console.log(`第${file.episode}集缩略图获取成功`);
                        }
                    } catch (error) {
                        console.error(`获取第${file.episode}集缩略图失败:`, error);
                        // 单个失败不影响其他缩略图的获取，继续处理下一个
                    }
                }

            } catch (error) {
                console.error('批量获取缩略图失败:', error);
            }
        }
    }, [mediaFiles]);

    useEffect(() => {
        // 调用接口获取媒体详情
        runGetMediaDetails({ media_id: media_id });
        // 调用接口获取媒体文件列表
        runGetMediaFiles({ lib_id: lib_id, media_id: media_id });
    }, [runGetMediaDetails, runGetMediaFiles, media_id, lib_id]);

    // 当mediaFiles更新后延迟获取缩略图
    useEffect(() => {
        if (mediaFiles && mediaFiles.length > 0 && classes !== '电影') {
            // 直接获取缩略图，不使用blob URL缓存（因为blob URL在页面刷新后会失效）
            console.log('开始获取缩略图，媒体文件数量:', mediaFiles.length);
            const timer = setTimeout(() => {
                fetchAllThumbnails();
            }, 500);

            return () => clearTimeout(timer);
        }
    }, [mediaFiles, fetchAllThumbnails, classes, media_id]);

    // 清理blob URLs
    useEffect(() => {
        // 清理sessionStorage中的无效缩略图缓存
        const cacheKey = `thumbnails_${media_id}`;
        sessionStorage.removeItem(cacheKey);

        return () => {
            // 组件卸载时清理所有blob URLs
            episodeThumbnails.forEach((url) => {
                if (url.startsWith('blob:')) {
                    URL.revokeObjectURL(url);
                }
            });
        };
    }, [episodeThumbnails, media_id]);

    // 当mediaFiles更新时，自动设置默认选择第一个版本（仅电影类型）
    useEffect(() => {
        if (classes === '电影' && mediaFiles && mediaFiles.length >= 2) {
            // 设置为第一个文件的ID
            setSelectedVersionId(mediaFiles[0].file_id.toString());
        }
    }, [mediaFiles, classes]);

    const videoInfo: VideoInfo = useMemo(() => {
        if (!mediaDetails) {
            // 如果还没有获取到数据，返回默认值
            return {
                title: '加载中...',
                score: '0',
                year: '0',
                category: '加载中',
                duration: '0分钟',
                source: '媒体库名称',
                posterUrl: '',
                actors: [],
                fileInfo: {
                    path: '',
                    size: ''
                },
                synopsis: '加载中...',
                versions: [],
                video_length: ''
            };
        }

        // 根据mediaFiles生成版本选择项（仅在电影类型时使用）
        const versions = (classes === '电影' && mediaFiles && mediaFiles.length >= 2)
            ? mediaFiles.map((file) => ({
                id: file.file_id.toString(),
                name: `${file.resolution} | ${file.hdr} | ${file.audio_codec}`,
                path: file.path // 添加path字段用于播放
            }))
            : [
                { id: '1', name: '4K | Dolby Vision | 内嵌字幕' }, // 默认版本信息
                { id: '2', name: '4K | Dolby Vision | 无内嵌字幕' },
                { id: '3', name: '1080P | HDR10 | 内嵌字幕' }
            ];

        // 使用接口返回的真实数据
        return {
            title: mediaDetails.trans_name || mediaDetails.origin_name || '未知影片',
            score: (mediaDetails.score && mediaDetails.score > 0) ? mediaDetails.score : '暂无评分',
            year: mediaDetails.year?.toString() || '未知',
            category: `${mediaDetails.classes} / ${mediaDetails.origin_place}`,
            duration: (() => {
                const totalMinutes = mediaDetails.video_length;
                if (!totalMinutes) return '未知时长';

                const hours = Math.floor(totalMinutes / 60);
                const minutes = totalMinutes % 60;

                if (hours === 0) {
                    return `${minutes}分钟`;
                } else if (minutes === 0) {
                    return `${hours}小时`;
                } else {
                    return `${hours}小时${minutes}分钟`;
                }
            })(),
            source: '媒体库名称',
            posterUrl: mediaDetails.poster?.[1] ?? mediaDetails.poster?.[0] ?? no_detaile,
            actors: mediaDetails.actor_info?.map(actor => ({
                name: actor?.name || '未知演员',
                role: actor.chapter ? `饰 ${actor.chapter}` : '演员',
                poster: actor.profile_path || ''
            })) || [],
            video_length: mediaDetails.video_length?.toString() || '',
            fileInfo: {
                path: '存储空间/影视文件路径', // 这个字段在接口中没有，保持默认
                size: '未知大小' // 这个字段在接口中没有，保持默认
            },
            synopsis: mediaDetails.brief || '暂无简介',
            versions
        };
    }, [mediaDetails, mediaFiles, classes]);

    // 创建一个稳定的缩略图映射对象用于FilmList依赖
    const thumbnailsForFilmList = useMemo(() => {
        const obj: { [key: number]: string } = {};
        episodeThumbnails.forEach((url, fileId) => {
            obj[fileId] = url;
        });
        return obj;
    }, [episodeThumbnails]);

    const FilmList: IFilmCard[] = useMemo(() => {
        // 如果没有媒体文件数据，返回空数组
        if (!mediaFiles || mediaFiles.length === 0 || !mediaDetails) {
            return [];
        }

        // 将媒体文件数据转换为 IFilmCard 格式
        return mediaFiles.filter(file => file && file.file_id && typeof file.file_size === 'number').map((file) => {
            // 获取该集的缩略图，如果没有则使用默认poster
            const thumbnailUrl = thumbnailsForFilmList[file.file_id];
            const posterUrl = thumbnailUrl || (mediaDetails?.poster && mediaDetails.poster.length > 0 ? mediaDetails.poster[0] : '');

            const cardData = {
                poster: posterUrl,
                progress: file.last_play_point > 0 ? Math.min(Math.round(file.last_play_point), 100) : 0, // 播放进度，确保不超过100
                title: `第${file.episode}集`,
                time: file.last_play_point > 0 ? '已观看' : '未观看',
                score: (mediaDetails.score && mediaDetails.score > 0) ? mediaDetails.score : '暂无评分',
                year: mediaDetails?.year?.toString() || '未知',
                setNum: file.episode, // 使用episode集数
                duration: file.duration || 0, // 从接口数据中没有这个字段，设置为数字类型
                source: '媒体库名称',
                posterUrl: posterUrl,
                actors: mediaDetails?.actor_info?.map(actor => ({
                    name: actor?.name || '未知演员',
                    role: actor.chapter ? `饰 ${actor.chapter}` : '演员'
                })) || [],
                fileInfo: {
                    path: file.path,
                    size: formatFileSize(file.file_size || 0) // 使用file_size字段并格式化
                },
                synopsis: mediaDetails?.brief || '暂无简介',
                versions: [
                    { id: file.file_id.toString(), name: `${file.resolution} | ${file.hdr} | ${file.audio_codec}` }
                ],
                // 添加文件相关信息
                file_id: file.file_id,
                episode: file.episode,
                session: file.session,
                resolution: file.resolution,
                hdr: file.hdr,
                audio_codec: file.audio_codec,
                last_play_point: file.last_play_point,
                name: mediaDetails?.trans_name || mediaDetails?.origin_name || '未知影片', // 添加影片名称
                media_id: media_id, // 添加media_id
                lib_id: lib_id,      // 添加lib_id
                file_media_id: file?.media_id,
                kind: mediaDetails?.kind || [],
                origin_place: mediaDetails?.origin_place || '',
                // 添加完整的剧集列表，用于播放器的下一集功能
                allMediaFiles: mediaFiles
            };

            return cardData;
        });
    }, [mediaFiles, mediaDetails, media_id, lib_id, thumbnailsForFilmList]); // 使用稳定的对象依赖


    // 搜索请求
    const { run: runSearchOnlineMedia } = useRequest(
        async (keyword: string) => {
            if (!keyword.trim()) return [];
            const res = await searchOnlineMedia(keyword);
            let data: mediaProps[] = [];
            if (Array.isArray(res.data)) {
                data = res.data;
            } else if (res.data && Array.isArray((res.data as any).medias)) {
                data = (res.data as any).medias;
            }
            return data;
        },
        {
            manual: true,
            onSuccess: (data) => {
                if (!data || !data.length) {
                    setSearchStatus('empty');
                    setSearchResults([]);
                } else {
                    setSearchStatus('success');
                    setSearchResults(data);
                }
            },
            onError: () => {
                setSearchStatus('error');
                setSearchResults([]);
            },
        }
    );

    const { run: runRematchMedia } = useRequest(
        rematchMedia,
        {
            manual: true,
            onSuccess: (res) => {
                if (res && res.code === 0) {
                    Toast.show({
                        content: '修正成功',
                        position: 'bottom',
                        duration: 1500,
                    });

                    // 设置显示刷新成功提示
                    setShowRefreshToast(true);

                    // 修正成功后，使用返回的new_media_id重新获取媒体详情和文件列表
                    const newMediaId = res.data?.new_media_id || media_id;

                    // 使用新的media_id获取详情
                    runGetMediaDetails({ media_id: newMediaId });
                    runGetMediaFiles({ lib_id: lib_id, media_id: newMediaId });
                } else {
                    Toast.show({
                        content: `修正失败，请重试`,
                        position: 'bottom',
                        duration: 1500,
                    });
                }
            },
            onError: (err) => {
                console.log('err: ', err);
            },
        }
    );

    const { run: runMediaDelete } = useRequest(
        mediaDelete,
        {
            manual: true,
            onSuccess: (res) => {
                if (res && res.code === 0) {
                    Toast.show({
                        content: '删除成功',
                        position: 'bottom',
                        duration: 1500,
                    });
                } else {
                    Toast.show({
                        content: `删除失败，请重试`,
                        position: 'bottom',
                        duration: 1500,
                    });
                }
            },
            onError: (err) => {
                console.log('err: ', err);
            },
        }
    );

    const { run: runMove2trashbin } = useRequest(
        move2trashbin,
        {
            manual: true,
            onSuccess: (res) => {
                if (res && res.code === 0) {
                    // 保存任务ID
                    if (res.data && res.data.task_id) {
                        setCurrentTaskId(res.data.task_id);
                        // 显示删除进度弹窗
                        // showDeleteProgressModal(res.data.task_id);

                        // 直接显示删除成功提示
                        Toast.show({
                            content: '删除成功',
                            position: 'bottom',
                            duration: 1500,
                        });

                        // 延迟返回上一页
                        setTimeout(() => {
                            history.push({
                                pathname: '/filmAndTelevisionWall_app/all',
                                state: { shouldRefresh: true }
                            });
                        }, 500);
                    } else {
                        Toast.show({
                            content: '删除成功',
                            position: 'bottom',
                            duration: 1500,
                        });
                    }
                } else {
                    Toast.show({
                        content: `删除失败，请重试`,
                        position: 'bottom',
                        duration: 1500,
                    });
                }
            },
            onError: (err) => {
                console.log('err: ', err);
            },
        }
    );

    const { run: runGetFilePath } = useRequest(
        getFilePath,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0 && res.data && res.data.path) {
                    // 获取到文件路径后，调用移动到回收站接口
                    runMove2trashbin({ path: res.data.path });
                    // 不再这里显示Toast，由runMove2trashbin的回调处理
                } else {
                    Toast.show({
                        content: '获取文件路径失败',
                        position: 'bottom',
                        duration: 1500,
                    });
                }
            },
            onError: () => {
                Toast.show({
                    content: '获取文件路径失败',
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }
    );

    // 处理搜索
    const handleSearch = useCallback((keyword: string) => {
        setSearchKeyword(keyword);
        if (!keyword.trim()) return;
        setSearchStatus('loading');
        runSearchOnlineMedia(keyword);
    }, [runSearchOnlineMedia]);

    // 处理选择搜索结果
    const handleSelectMatch = useCallback((item: mediaProps) => {
        setSelectedMatch(item);
    }, []);

    // 处理确认修正
    const handleConfirmMatch = useCallback((selectedItem: mediaProps | null) => {
        if (selectedItem) {
            const index = (selectedItem as any).index !== undefined ? (selectedItem as any).index : 0;
            runRematchMedia({
                media_ids: [media_id],
                lib_id: lib_id,
                keyword: searchKeyword,
                index,
            });

            // 关闭修正界面
            setShowMatchCorrection(false);

            // 清空搜索状态
            setSearchKeyword('');
            setSearchResults([]);
            setSelectedMatch(null);
            setSearchStatus('idle');
        } else {
            Toast.show({
                content: '请先选择一个匹配结果',
                position: 'bottom',
                duration: 1500,
            });
        }
    }, [runRematchMedia, media_id, lib_id, searchKeyword]);

    // 重置搜索状态
    const handleResetSearchStatus = useCallback(() => {
        setSearchStatus('idle');
        setSearchResults([]);
        setSelectedMatch(null);
    }, []);


    const move2Trashbin = useCallback((m) => {
        m.destroy();
        if (media_id) {
            runMediaDelete({ media_ids: [media_id], lib_id: lib_id });
        }
        // 仅从媒体库移除时不显示删除进度弹窗，直接提示成功
        Toast.show({
            content: '已从媒体库移除',
            position: 'bottom',
            duration: 1500,
        });
        history.push({
            pathname: '/filmAndTelevisionWall_app/all',
            state: { shouldRefresh: true }
        });
    }, [runMediaDelete, media_id, lib_id, history]);

    const delFile = useCallback((modal) => {
        modalShow(`是否确定删除文件？`, <>删除的文件将移至"回收站"，保留30天</>, (m => {
            m.destroy();
            modal.destroy();

            // 先获取文件路径，然后再移动到回收站
            runGetFilePath({ media_ids: [media_id], lib_id: lib_id });
        }), () => null, false, { position: 'bottom', okBtnText: '确定', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } })
    }, [media_id, lib_id, runGetFilePath])

    // 处理删除
    const handleDelete = useCallback(() => {
        setMorePopoverVisible(false);
        const m = modalShow('确认删除吗？', (
            <>
                <div className={styles.modalButton} onClick={() => move2Trashbin(m)}>仅从媒体库移除</div>
                <div className={styles.modalButton} style={{ color: 'var(--emergency-text-color)' }} onClick={() => delFile(m)}>删除文件</div>
            </>
        ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'bottom' })
    }, [delFile, move2Trashbin]);

    // 处理修改匹配信息
    const handleEditMatch = useCallback(() => {
        setMorePopoverVisible(false);
        setShowMatchCorrection(true);
        setSearchStatus('idle');
        setSearchResults([]);
        setSelectedMatch(null);
    }, []);

    const rightSize = useMemo(() => {
        return (
            <Popover
                className={styles.morePopoverContainer}
                visible={morePopoverVisible}
                onVisibleChange={setMorePopoverVisible}
                content={
                    <div className={styles.morePopover}>
                        <div className={styles.morePopoverItem} onClick={handleEditMatch}>
                            <span className={styles.morePopoverText} style={{ color: 'var(--text-color)' }}>修正匹配信息</span>
                        </div>
                        <div className={styles.morePopoverItem} style={{ color: 'var(--emergency-text-color)' }} onClick={handleDelete}>
                            <span className={styles.morePopoverText}>删除</span>
                        </div>
                    </div>
                }
                trigger='click'
                placement='bottom-end'
                style={{ '--arrow-size': '0px' } as React.CSSProperties}
            >
                <div className={styles.right} onClick={() => setMorePopoverVisible(true)}>
                    <PreloadImage src={moreIcon} alt="more" />
                </div>
            </Popover>
        )
    }, [morePopoverVisible, handleEditMatch, handleDelete]);

    const toggleSynopsis = useCallback(() => {
        setShowFullSynopsis(prev => !prev);
    }, []);

    const toggleFavorite = useCallback(() => {
        const newFavoriteStatus = !isFavorite;
        setIsFavorite(newFavoriteStatus);

        // 调用收藏API
        runCollect({
            media_ids: [media_id],
            favourite: newFavoriteStatus ? 1 : 0
        });
    }, [isFavorite, runCollect, media_id]);

    const toggleCheck = useCallback(() => {
        const newSeenStatus = !isCheck;
        setIsCheck(newSeenStatus);

        // 调用已观看API
        runMarkWatched({
            media_ids: [media_id],
            seen: newSeenStatus ? 1 : 0
        });
    }, [isCheck, runMarkWatched, media_id]);

    // 处理播放按钮点击
    const handlePlay = useCallback(() => {
        if (!mediaFiles || mediaFiles.length === 0) {
            Toast.show({
                content: '暂无可播放的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        // 构建完整的videoList数组
        const videoList = mediaFiles.map(file => ({
            path: file.path,
            media_id: (file.media_id || media_id).toString(), // 如果file.media_id为空，使用页面的media_id
            file_id: file.file_id.toString(),
            duration: file.duration || 0, // 视频时长
            position: file.last_play_point || 0, // 断点信息
            isCompelete: file.seen, // 是否完整播放，转换为boolean
            audioIndex: file?.audio_index, // 音轨信息，默认为0
            subtitlePath: file.subtitle_path, // 字幕路径，暂时为空
            subtitleType: file.subtitle_type, // 字幕类型，0表示内嵌字幕
            subtitleIndex: file.subtitle_index, // 字幕索引，默认为0
        }));

        let playIndex = 0; // 默认播放第一集

        // 根据classes判断播放逻辑
        if (classes === '电影') {
            // 电影：根据版本选择器确定播放索引
            if (mediaFiles.length >= 2) {
                const selectedFileIndex = mediaFiles.findIndex(file => file.file_id.toString() === selectedVersionId);
                if (selectedFileIndex !== -1) {
                    playIndex = selectedFileIndex;
                }
            }
        } else {
            // 剧集类型：根据media_details中的last_seen_file_id在file_list数组中筛选file_id相同的项的索引位置
            if (mediaDetails?.last_seen_file_id) {
                // 遍历mediaFiles数组，找到file_id与last_seen_file_id相同的项的索引
                const targetIndex = mediaFiles.findIndex(file => file.file_id === mediaDetails.last_seen_file_id);
                if (targetIndex !== -1) {
                    playIndex = targetIndex;
                    // console.log(`剧集播放：找到last_seen_file_id(${mediaDetails.last_seen_file_id})对应的索引位置：${targetIndex}`);
                } else {
                    // console.log(`剧集播放：未找到last_seen_file_id(${mediaDetails.last_seen_file_id})对应的文件，将播放第一集`);
                }
            } else {
                // console.log('剧集播放：没有last_seen_file_id，将播放第一集');
            }
        }

        // 调用视频播放接口
        playVideo(videoList, playIndex, (res) => {
            if (res.code === 0) {
                Toast.show({
                    content: '开始播放',
                    position: 'bottom',
                    duration: 1500,
                });
            } else {
                Toast.show({
                    content: `播放失败: ${res.msg}`,
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }).catch((error) => {
            Toast.show({
                content: error.message || '播放失败',
                position: 'bottom',
                duration: 1500,
            });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [mediaFiles, classes, mediaDetails, selectedVersionId]);

    // 处理下载按钮点击
    const handleDownload = useCallback(() => {
        if (hasDownloaded) {
            return; // 如果已经下载过，直接返回
        }

        if (!mediaFiles || mediaFiles.length === 0) {
            Toast.show({
                content: '暂无可下载的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        // 构造文件信息列表，过滤掉无效的文件数据
        const validFiles = mediaFiles.filter(file => file && file.path && typeof file.file_size === 'number');
        if (validFiles.length === 0) {
            Toast.show({
                content: '没有可下载的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        const fileList = validFiles.map(file => ({
            name: file.path.split('/').pop() || `第${file.episode || 1}集`,
            path: file.path,
            mtime: '', // mediaFiles中没有修改时间信息，设为空字符串
            size: file?.file_size || 0
        }));

        // 标记为已下载，不管成功与否都不能再次点击
        setHasDownloaded(true);

        // 调用下载接口
        downloadFiles(fileList, (res) => {
            if (res.code === 0) {
                const m = modalShow(
                    '下载位置',
                    (
                        <>
                            <div style={{ fontFamily: 'MiSans', fontWeight: '400', textAlign: 'center', fontSize: '16px', color: 'var(--text-color)', padding: '0 20px 28px 20px' }}>
                                手机本地路径/手机本地路径
                            </div>
                            <div className={styles.modalButton} onClick={() => m.destroy()}>知道了</div>
                        </>
                    ),
                    () => null,
                    () => null,
                    false,
                    {
                        okBtnStyle: { display: 'none' },
                        cancelBtnStyle: { display: 'none' },
                        position: 'bottom',
                    }
                );
                Toast.show({
                    content: '开始下载',
                    position: 'bottom',
                    duration: 1500,
                });
            } else {
                Toast.show({
                    content: `下载失败: ${res.msg}`,
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }).catch((error) => {
            Toast.show({
                content: error.message || '下载失败',
                position: 'bottom',
                duration: 1500,
            });
        });
    }, [mediaFiles, hasDownloaded]);

    const handleVersionSelect = useCallback((versionId: string) => {
        setSelectedVersionId(versionId);
        // 自动隐藏Popover
        setPopoverVisible(false);
        Toast.show({
            content: '已切换版本',
            position: 'bottom',
            duration: 1500,
        });
    }, []);

    // 获取当前选择的版本名称
    const selectedVersionName = useMemo(() => {
        const selectedVersion = videoInfo.versions.find(version => version.id === selectedVersionId);
        return selectedVersion ? selectedVersion?.name : videoInfo.versions[0]?.name;
    }, [selectedVersionId, videoInfo.versions]);

    // 获取当前选择的版本对应的文件信息
    const selectedVersionFile = useMemo(() => {
        if (classes === '电影' && mediaFiles && mediaFiles.length >= 2) {
            // 电影类型：根据selectedVersionId找到对应的文件
            const selectedFile = mediaFiles.find(file => file.file_id.toString() === selectedVersionId);
            return selectedFile || mediaFiles[0]; // 如果没找到，返回第一个文件
        } else {
            // 非电影类型：返回第一个文件
            return mediaFiles && mediaFiles.length > 0 ? mediaFiles[0] : null;
        }
    }, [classes, mediaFiles, selectedVersionId]);

    useEffect(() => {
        setHeaderStyle({
            backgroundImage: `url(${videoInfo.posterUrl})`
        });
    }, [videoInfo.posterUrl]);

    // 计算播放按钮显示文本
    const getPlayButtonText = useMemo(() => {
        // 如果是电影类型，显示播放时间
        if (classes === '电视剧') {
            // 如果是剧集类型，显示上次播放的集数
            const lastSeenFileId = mediaDetails?.last_seen_file_id;
            if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
                const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
                if (lastSeenFile && lastSeenFile.episode) {
                    return `播放 第${lastSeenFile.episode}集`;
                }
            }
            // 没有播放记录时显示第1集
            return mediaFiles && mediaFiles.length > 0 ? '播放 第1集' : '播放';
        } else {
             // 根据 mediaDetails 中的 last_seen_file_id 去 mediaFiles 中找到匹配的文件
            const lastSeenFileId = mediaDetails?.last_seen_file_id;
            if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
                const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
                if (lastSeenFile) {
                    const lastPlayPoint = lastSeenFile.last_play_point || 0;
                    if (lastPlayPoint > 0) {
                        const minutes = Math.floor(lastPlayPoint / 60);
                        const seconds = lastPlayPoint % 60;
                        return `播放 ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                    }
                }
            }
            return '播放';
        }
    }, [classes, mediaDetails, mediaFiles]);


    // 处理演员点击事件
    const handleActorClick = useCallback((actor: Actor) => {
        // 跳转到演员详情页，传递演员的profile_path和name
        const params = new URLSearchParams({
            profile_path: actor.poster || '',
            name: actor.name || '未知演员'
        });

        history.push(`/filmAndTelevisionWall_app/all/videoDetails/actorDetails?${params.toString()}`);
    }, [history]);

    // 智能返回逻辑
    const handleBack = useCallback(() => {
        // 如果是从 Library 页面跳转过来的，且有 lib_id，则返回到对应的 Library 页面
        if (lib_id && lib_id !== 0 && stateParams.fromLibrary && stateParams.libraryTitle) {
            history.push({
                pathname: '/filmAndTelevisionWall_app/library',
                state: {
                    title: lib_title,
                    lib_id: lib_id
                }
            });
        } else if (lib_id && lib_id !== 0) {
            // 如果有 lib_id 但没有明确的来源信息，尝试返回到 Library 页面
            // 这里可以根据 lib_id 获取库名称，或者使用默认名称
            history.push({
                pathname: '/filmAndTelevisionWall_app/library',
                state: {
                    title: lib_title, // 默认标题，实际应用中可以通过 API 获取真实的库名称
                    lib_id: lib_id
                }
            });
        } else {
            // 其他情况使用默认的返回逻辑
            history.goBack();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [history, lib_id, stateParams.fromLibrary, stateParams.libraryTitle]);

    const renderActors = useMemo(() => (
        videoInfo.actors.map((actor, index) => (
            <div key={index} className={styles.castItem} onClick={() => handleActorClick(actor)}>
                <div className={styles.actorAvatar}>
                    <PreloadImage src={actor?.poster || no_avatar} alt="actor" />
                </div>
                <div className={styles.actorName}>{actor?.name}</div>
                <div className={styles.actorRole}>{actor?.role}</div>
            </div>
        ))
    ), [videoInfo.actors, handleActorClick]);


    const renderSynopsis = useMemo(() => {
        const isLongText = videoInfo.synopsis.length > 73;

        if (!isLongText) {
            // 如果文本不够长，直接显示全部内容，不显示展开/收起按钮
            return (
                <div className={styles.fullSynopsis}>
                    {videoInfo.synopsis}
                </div>
            );
        }

        return showFullSynopsis ? (
            <div className={styles.fullSynopsis}>
                {videoInfo.synopsis}
                <span className={styles.more} onClick={toggleSynopsis}>收起</span>
            </div>
        ) : (
            <div className={styles.limitedSynopsis} ref={synopsisRef}>
                <span>{videoInfo.synopsis.substring(0, 70)}...</span>
                <span className={styles.more} onClick={toggleSynopsis}>更多</span>
            </div>
        );
    }, [showFullSynopsis, videoInfo.synopsis, toggleSynopsis]);

    return (
        <div className={styles.container}>
            <SearchSelector
                visible={showMatchCorrection}
                onClose={() => setShowMatchCorrection(false)}
                onSearch={handleSearch}
                onConfirm={handleConfirmMatch}
                // searchStatus={searchStatus === 'loading' ? (searchLoading ? 'loading' : 'idle') : searchStatus}
                searchStatus={searchStatus}
                searchResults={searchResults}
                selectedItem={selectedMatch}
                onSelectItem={handleSelectMatch}
                placeholder="请输入匹配的影片名称"
                confirmButtonText="确定修正"
                errorMessage="搜索失败,请重试"
                emptyMessage="没搜索到相关结果"
                itemType="movie"
                onResetStatus={handleResetSearchStatus}
                keyField="media_id"
            />
            {!showMatchCorrection && (
                <div className={styles.scrollContainer}>
                    {/* 顶部背景和电影信息 */}
                    <div className={styles.headerSection} style={headerStyle}>
                        <div className={styles.posterOverlay}>
                            <NavigatorBar
                                right={rightSize}
                                backIconTheme="dark"
                                onBack={handleBack}
                            />
                        </div>
                        <div className={styles.movieInfo}>
                            <h1 className={styles.title}>{videoInfo.title}</h1>
                            {classes !== '其他' && (<div className={styles.metaInfo}>
                                <PreloadImage src={douBan_icon} style={{width:'18px',height:'18px'}} alt='豆瓣icon' />
                                <span className={styles.score}>
                                    {typeof videoInfo.score === 'number' ? `${videoInfo.score} 分` : videoInfo.score}
                                </span>
                                <span className={styles.divider}>/</span>
                                <span>{videoInfo.year}</span>
                                <span className={styles.divider}>/</span>
                                <span>{videoInfo.category}</span>
                                <span className={styles.divider}>/</span>
                                <span>{videoInfo.duration}</span>
                            </div>)}

                            {/* <div className={styles.sourceInfo}>
                                来自 "{videoInfo.source}"
                            </div> */}
                        </div>
                    </div>

                    {/* 画质标签 */}
                    {classes !== '其他' && selectedVersionFile && (
                        <div className={styles.qualityTags}>
                            {/* 分辨率标签 - 只显示指定值 */}
                            {selectedVersionFile.resolution && ['4K', '蓝光', '1080P', '720P'].includes(selectedVersionFile.resolution) && (
                                <Tag color="#D8D8D8" className={styles.qualityTag}>{selectedVersionFile.resolution}</Tag>
                            )}
                            {selectedVersionFile.hdr && <Tag color="#D8D8D8" className={styles.qualityTag}>{selectedVersionFile.hdr}</Tag>}
                            {selectedVersionFile.audio_codec && <Tag color="#D8D8D8" className={styles.qualityTag}>{selectedVersionFile.audio_codec}</Tag>}
                        </div>
                    )}

                    {/* 版本选择器 - 只在电影类型且有多个版本时显示 */}
                    {classes === '电影' && mediaFiles && mediaFiles.length >= 2 && (
                        <div className={styles.versionSelector}>
                            <Popover
                                style={{ '--arrow-size': '0px' } as React.CSSProperties}
                                className={styles.versionSelectorContent}
                                content={
                                    <div className={styles.versionList}>
                                        {videoInfo?.versions?.map((version, index) => (
                                            <React.Fragment key={version.id}>
                                                <div
                                                    className={`${styles.versionItem} ${selectedVersionId === version.id ? styles.versionItemSelected : ''}`}
                                                    onClick={() => handleVersionSelect(version.id)}
                                                >
                                                    <span className={styles.versionItemText}>{version?.name}</span>
                                                    {selectedVersionId === version.id && <CheckOutline className={styles.versionItemCheckIcon} />}
                                                </div>
                                                {index < videoInfo?.versions?.length - 1 && <Divider className={styles.versionDivider} />}
                                            </React.Fragment>
                                        ))}
                                    </div>
                                }
                                trigger='click'
                                placement='bottom'
                                visible={popoverVisible}
                                onVisibleChange={setPopoverVisible}
                            >
                                <div className={styles.versionButton} onClick={() => setPopoverVisible(true)}>
                                    <span className={styles.versionButtonText}>{selectedVersionName}</span>
                                    <span className={styles.versionButtonIcon}><PreloadImage src={updon} alt="select" /></span>
                                </div>
                            </Popover>
                        </div>
                    )}



                    {/* 操作按钮 */}
                    <div className={styles.actionButtons}>
                        <Button className={styles.playButton} color='primary' size='small' onClick={handlePlay}>
                            <PlayOutline />
                            <span className={styles.playfont}>{getPlayButtonText}</span>

                        </Button>

                        <Button
                            className={styles.iconButton}
                            onClick={handleDownload}
                            disabled={hasDownloaded}
                        >
                            <DownlandOutline color={hasDownloaded ? '#999999' : '#ffffff'} fontSize={25} />
                        </Button>
                        <Button
                            className={styles.iconButton}
                            onClick={toggleFavorite}
                        >
                            {isFavorite ?
                                <HeartFill color='#FA311B' fontSize={25} /> :
                                <HeartOutline color='#ffffff' fontSize={25} />
                            }
                        </Button>
                        <Button className={styles.iconButton} onClick={toggleCheck}>
                            {isCheck ?
                                <CheckOutline color='#0080FF' fontSize={25} /> :
                                <CheckOutline color='#ffffff' fontSize={25} />
                            }
                        </Button>
                    </div>

                    {/* 影片简介 */}
                    {classes !== '其他' && (
                        <div className={styles.synopsis}>
                            {renderSynopsis}
                        </div>
                    )}

                    {/* 若是电视剧 */}
                    {classes === '电视剧' && mediaFiles.length >= 2 && (
                        <div className={styles.dramaInfo}>
                            <FilmCardList type='add' list={FilmList} isDrama={true} />
                        </div>
                    )}

                    {/* 演员列表 */}
                    {classes !== '其他' && (
                        <div className={styles.castSection}>
                            <h2 className={styles.sectionTitle}>演职人员</h2>
                            <div className={styles.castList}>
                                {renderActors}
                            </div>
                        </div>
                    )}


                    {/* 文件信息 */}
                    {selectedVersionFile && (
                        <div className={styles.fileInfoSection}>
                            <h2 className={styles.sectionTitle}>文件信息</h2>
                            <div className={styles.fileInfoBox}>
                                <div className={styles.fileInfoItem}>
                                    <div>
                                        <span className={styles.fileInfoLabel}>文件路径：</span>
                                        <span className={styles.fileInfoValue}>{CommonUtils.formatFilePath(selectedVersionFile?.path) || '未知路径'}</span>
                                    </div>
                                </div>
                                <div className={styles.fileInfoItem}>
                                    <div>
                                        <span className={styles.fileInfoLabel}>文件大小：</span>
                                        <span className={styles.fileInfoValue}>
                                            {selectedVersionFile?.file_size ? formatFileSize(selectedVersionFile.file_size) : '未知大小'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default VideoDetails;

