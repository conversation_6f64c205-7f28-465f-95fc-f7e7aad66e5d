.episodeListContainer {
  height: 200px;
}


.episodeListWrapper {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
}

.scrollArrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  color: white;
  position: absolute;
  z-index: 10;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  &:first-child {
    left: -20px;
  }
  
  &:last-child {
    right: -20px;
  }
}
.episodeList {
  display: flex;
  overflow-x: auto;
  padding: 10px 0;
  gap: 15px;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.episodeItem {
  min-width: 180px;
  cursor: pointer;
  transition: transform 0.2s;
  
  &:hover {
    transform: scale(1.05);
  }
}


.episodeList_tv {
  display: flex;
  height: 300px;
  overflow-x: auto;
  padding: 10px 0;
  gap: 15px;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.episodeItem_tv {
  min-width: 385px;
  height: 216px;
  display: flex;
  flex-direction: column;
  align-items: start;
  transition: transform 0.2s;
  margin-right: 20px;
  &:hover {
    transform: scale(1.05);
  }
}

.episodeItemActive {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: #1890ff;
    border-radius: 2px;
  }
}

.episodeThumbnail {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
  border: 1px solid #fff;
  img {
    position: absolute;
    border-radius: 15px;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

/* 遮罩层样式 */
.hoverOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s;
  opacity: 1;
}

/* 中央播放按钮 */
.playButton {
  // font-size: 48px;
  // color: white;
  // background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  text-align: center;
  border: 1px solid white;
  border-radius: 50%;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);

}

/* 右下角操作按钮区 */
.actionButtons {
  position: absolute;
  right: 0px;
  bottom: -2px;
  display: flex;
  gap: 0px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  color: white;
  font-size: 16px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.watchedBadge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #1890ff;
}

.progressBarContainer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.5);
}

.progressBar {
  height: 100%;
  background-color: #1890ff;
  border-radius: 0 2px 2px 0;
}

.episodeNumber_tv {
  display: flex;
  justify-content: start;
  align-items: center;
  font-size: 30px;
  font-weight: 400;
  text-align: center;
  color: rgba(255, 255, 255, 0.85);
  >span{
    margin-right: 20px;
  }
} 
.episodeNumber {
  display: flex;
  justify-content: start;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  text-align: center;
  color: rgba(255, 255, 255, 0.85);
  >span{
    margin-right: 20px;
  }
} 

/* 更多菜单样式 */
.moreMenu {
  min-width: 70px;
  // background-color: #1f1f1f;
  border-radius: 4px;
  padding: 4px 0;
}

.moreMenuItem {
  padding: 4px 2px;
  font-size: 8px;
  color: #000;
  cursor: pointer;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.modal_button {
  width: 100%;
  height: 50px;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  border-radius: 16px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color);
  cursor: pointer;

  &:hover {
    background-color: #fff;
    color: #4096ff;
  }
}

// TVFocusable 焦点样式
.focus_item {
  &:focus {
    outline: none;
    
    // 剧集项焦点效果
    &.episodeItem_tv {
      transform: scale(1.05);
      transition: transform 0.3s ease;
      position: relative;
      
      // 蓝色流光边框效果
      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg, 
          #00bfff, #1e90ff, #4169e1, #0066ff, 
          #00bfff, #1e90ff, #4169e1, #0066ff);
        background-size: 400% 400%;
        border-radius: 12px;
        z-index: -1;
        animation: streamingLight 2s ease-in-out infinite;
      }
      
      // 内层蓝色光晕
      &::after {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(45deg, 
          rgba(0, 191, 255, 0.3), 
          rgba(30, 144, 255, 0.3), 
          rgba(65, 105, 225, 0.3), 
          rgba(0, 102, 255, 0.3));
        border-radius: 10px;
        z-index: -1;
        filter: blur(2px);
      }
    }
    
    // 滚动箭头焦点效果
    &.scrollArrow {
      transform: scale(1.1);
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg, 
          #00bfff, #1e90ff, #4169e1, #0066ff, 
          #00bfff, #1e90ff, #4169e1, #0066ff);
        background-size: 400% 400%;
        border-radius: 50%;
        z-index: -1;
        animation: streamingLight 2s ease-in-out infinite;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(45deg, 
          rgba(0, 191, 255, 0.3), 
          rgba(30, 144, 255, 0.3));
        border-radius: 50%;
        z-index: -1;
        filter: blur(1px);
      }
    }
  }
}

// 流光动画
@keyframes streamingLight {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}