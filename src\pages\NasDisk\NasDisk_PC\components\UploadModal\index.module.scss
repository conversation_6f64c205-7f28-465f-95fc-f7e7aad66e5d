.uploadModal {
  :global {
    .ant-modal-content {
      background-color: var(--desktop-modal-bg-color);
      border-radius: 32px;
      overflow: hidden;
    }

    .ant-modal-header {
      padding: 0;
      margin-bottom: 0;
      border-bottom: none;
    }

    .ant-modal-title {
      padding: 0;
      margin: 0;
      line-height: 1;
    }

    .ant-modal-body {
      padding: 0;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--desktop-modal-bg-color);

  .backIcon {
    font-size: 18px;
    color: var(--text-color);
    cursor: pointer;
    margin-right: 16px;
  }

  .modalTitle {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
    margin: 0 16px; /* 左右平衡间距 */
  }
}

.breadcrumbHeader {
  display: flex;
  align-items: center;
  padding: 16px 10px;
  border-bottom: 1px solid var(--border-color);
}

.breadcrumbContainer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .breadcrumbItem {
    padding: 4px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;

    &.breadcrumbLink {
      color: rgba(140, 147, 176, 1);
      background-color: rgba(140, 147, 176, 0.1);
    }

    &.breadcrumbCurrent {
      color: rgba(255, 178, 29, 1);
      background-color: rgba(255, 178, 29, 0.15);
    }
  }

  .breadcrumbSeparator {
    margin: 0 8px;
    color: var(--list-value-text-color);
  }
}

.selectAllContainer {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 16px;
  transition: all 0.2s;

  &:hover {
    opacity: 0.8;
  }

  .selectAllIcon {
    width: 40px;
    height: 40px;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .selectAllText {
    font-size: 14px;
    color: var(--text-color);
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  height: 100%;

  .fileListContainer {
    flex: 1;
    overflow-y: auto;
    padding: 0;

    .fileList {
      width: 100%;

      :global {
        .ant-list-item {
          padding: 12px 24px;
          border-bottom: 1px solid var(--border-color);

          &:hover {
            background-color: var(--hover-background-color);
          }
        }
      }

      .fileItem {
        display: flex;
        align-items: center;
        justify-content: space-between;

        // &.selectedItem {
        //   background-color: var(
        //     --item-selected-background-color,
        //     rgba(0, 0, 0, 0.05)
        //   );
        // }

        .checkboxCell {
          margin-left: 12px;
        }

        .fileContent {
          display: flex;
          align-items: center;
          flex: 1;
          cursor: pointer;
          transition: all 0.2s;

          &.selectedContent {
            .fileName {
              color: var(--primary-color);
              font-weight: 600;
            }
          }

          &:hover {
            .fileName {
              color: var(--primary-color);
            }
          }

          .fileIcon {
            width: 36px;
            height: 36px;
            margin-right: 12px;
          }

          .fileInfo {
            flex: 1;

            .fileName {
              font-family: MiSans W;
              font-weight: 500;
              font-size: 16px;
              line-height: 22px;
              color: var(--text-color);
              margin-bottom: 4px;
            }

            .fileDetails {
              font-size: 12px;
              color: var(--list-value-text-color);
            }
          }
        }


      }
    }

    .loadingContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--list-value-text-color);
    }

    .emptyContainer {
      display: flex;
      align-items: center;
      justify-content: center;

      .emptyText {
        color: var(--list-value-text-color);
        font-size: 14px;
      }
    }
  }

  .footerContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;

    .uploadPathContainer {
      flex: 1;
      margin-right: 16px;
      position: relative;
      max-width: calc(100% - 175px - 16px); /* 减去上传按钮宽度和间距 */

      .uploadPathButton {
        width: 100%;
        text-align: left;
        padding: 14px 20px;
        height: auto;
        color: var(--primary-color);
        border-radius: 20px;
        border-color: var(--primary-color);
        display: flex;
        align-items: center;
        font-weight: 500;
        background-color: var(--card-active-background-color);

        .pathLabel {
          flex-shrink: 0;
          white-space: nowrap;
        }

        .pathContainer {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0; /* 重要：使flex子元素可以收缩到比内容小 */
        }

        .uploadPath {
          color: var(--primary-color);
          margin-left: 4px;
        }
      }
    }
    .vipTip {
      position: absolute;
      right: 0;
      bottom: 36px;
      transform: translateX(-50%);
      background-color: #402c00;
      color: #e2ae1e;
      padding: 2px 5px;
      border-radius: 5px;
      font-size: 12px;
      border: 0.1px solid #e2ae1e;
      z-index: 10;
    }

    .uploadButton {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      height: 50px;
      min-width: 175px;
      border-radius: 20px;

      &:disabled {
        background-color: var(--primary-color);
        color: rgba(255, 255, 255, 1);
        border: none;
        opacity: 0.3;
      }
    }
  }
}
